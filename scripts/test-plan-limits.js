#!/usr/bin/env node

/**
 * Test Plan Limits Script
 * 
 * This script tests the plan limits functionality to ensure the fix works correctly.
 * 
 * Usage: bun run scripts/test-plan-limits.js
 */

import { getPlanLimits } from '../src/lib/plan-utils.js'

console.log('🧪 Testing Plan Limits Functionality...')
console.log('─'.repeat(50))

// Test cases
const testCases = [
  {
    name: 'Free plan (no period)',
    planName: 'free',
    period: undefined,
    expected: {
      projects: 3,
      videoExports: 0,
      aiImages: 20,
      storage: 100,
      teamMembers: 1
    }
  },
  {
    name: 'Free plan with monthly period (should ignore period)',
    planName: 'free',
    period: 'monthly',
    expected: {
      projects: 3,
      videoExports: 0,
      aiImages: 20,
      storage: 100,
      teamMembers: 1
    }
  },
  {
    name: 'Free plan with annual period (should ignore period)',
    planName: 'free',
    period: 'annual',
    expected: {
      projects: 3,
      videoExports: 0,
      aiImages: 20,
      storage: 100,
      teamMembers: 1
    }
  },
  {
    name: 'Basic monthly plan',
    planName: 'basic',
    period: 'monthly',
    expected: {
      projects: 10,
      videoExports: 15,
      aiImages: 100,
      storage: 500,
      teamMembers: 1
    }
  },
  {
    name: 'Premium monthly plan',
    planName: 'premium',
    period: 'monthly',
    expected: {
      projects: 20,
      videoExports: 30,
      aiImages: 200,
      storage: 1024,
      teamMembers: 5
    }
  }
]

let passedTests = 0
let totalTests = testCases.length

console.log(`Running ${totalTests} test cases...\n`)

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.name}`)
  console.log(`Input: planName="${testCase.planName}", period=${testCase.period || 'undefined'}`)
  
  try {
    const result = getPlanLimits(testCase.planName, testCase.period)
    
    // Check if all expected properties match
    let testPassed = true
    const mismatches = []
    
    Object.keys(testCase.expected).forEach(key => {
      if (result[key] !== testCase.expected[key]) {
        testPassed = false
        mismatches.push({
          property: key,
          expected: testCase.expected[key],
          actual: result[key]
        })
      }
    })
    
    if (testPassed) {
      console.log('✅ PASSED')
      passedTests++
    } else {
      console.log('❌ FAILED')
      console.log('Mismatches:')
      mismatches.forEach(mismatch => {
        console.log(`  - ${mismatch.property}: expected ${mismatch.expected}, got ${mismatch.actual}`)
      })
    }
    
    console.log(`Result: ${JSON.stringify(result, null, 2)}`)
    
  } catch (error) {
    console.log('❌ ERROR:', error.message)
  }
  
  console.log('─'.repeat(30))
})

console.log('\n📊 Test Summary:')
console.log(`Passed: ${passedTests}/${totalTests}`)
console.log(`Failed: ${totalTests - passedTests}/${totalTests}`)

if (passedTests === totalTests) {
  console.log('🎉 All tests passed!')
  process.exit(0)
} else {
  console.log('❌ Some tests failed!')
  process.exit(1)
}
