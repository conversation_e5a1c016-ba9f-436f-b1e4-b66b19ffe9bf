#!/usr/bin/env node

/**
 * Verify User Plan System Script
 * 
 * This script verifies that the user plan system is working correctly by:
 * 1. Testing the usage API for different user types
 * 2. Verifying free users get correct limits
 * 3. Verifying paid users get correct limits
 * 4. Testing plan upgrade scenarios
 * 
 * Usage: bun run scripts/verify-user-plan-system.js [user-email]
 */

import { drizzle } from 'drizzle-orm/postgres-js'
import { eq } from 'drizzle-orm'
import postgres from 'postgres'
import { user, usage, subscription } from '../src/db/schema.js'
import { getPlanLimits } from '../src/lib/plan-utils.js'

// Database connection
const connectionString = process.env.DATABASE_URL
if (!connectionString) {
  console.error('❌ DATABASE_URL environment variable is required')
  process.exit(1)
}

const client = postgres(connectionString)
const db = drizzle(client)

// Expected free plan limits
const EXPECTED_FREE_LIMITS = {
  projects: 3,
  videoExports: 0,
  aiImages: 20,
  teamMembers: 1,
  storage: 100
}

async function verifyUser(email) {
  console.log(`🔍 Verifying user: ${email}`)
  console.log('─'.repeat(50))

  try {
    // Find user
    const users = await db
      .select()
      .from(user)
      .where(eq(user.email, email))
      .limit(1)

    if (users.length === 0) {
      console.log('❌ User not found')
      return false
    }

    const userData = users[0]
    const userId = userData.id

    // Get usage record
    const usageRecords = await db
      .select()
      .from(usage)
      .where(eq(usage.organizationId, userId))

    // Get subscription records
    const subscriptionRecords = await db
      .select()
      .from(subscription)
      .where(eq(subscription.referenceId, userId))

    console.log('📊 Current State:')
    console.log(`   Usage records: ${usageRecords.length}`)
    console.log(`   Subscription records: ${subscriptionRecords.length}`)

    if (usageRecords.length > 0) {
      const usageRecord = usageRecords[0]
      console.log(`   Plan type in usage: ${usageRecord.planType}`)
      console.log(`   Plan status: ${usageRecord.planStatus}`)
    }

    if (subscriptionRecords.length > 0) {
      subscriptionRecords.forEach((sub, index) => {
        console.log(`   Subscription ${index + 1}: ${sub.plan} (${sub.status})`)
      })
    }

    // Test plan limits calculation
    console.log('\n🧪 Testing Plan Limits:')
    
    if (usageRecords.length > 0) {
      const usageRecord = usageRecords[0]
      const planType = usageRecord.planType
      
      // Test getPlanLimits function
      const calculatedLimits = getPlanLimits(planType)
      console.log(`   Calculated limits for "${planType}":`)
      console.log(`   - Projects: ${calculatedLimits.projects}`)
      console.log(`   - Video Exports: ${calculatedLimits.videoExports}`)
      console.log(`   - AI Images: ${calculatedLimits.aiImages}`)
      console.log(`   - Team Members: ${calculatedLimits.teamMembers}`)
      console.log(`   - Storage: ${calculatedLimits.storage}MB`)

      // Verify free plan limits
      if (planType.toLowerCase() === 'free') {
        console.log('\n✅ Verifying Free Plan Limits:')
        let allCorrect = true
        
        Object.keys(EXPECTED_FREE_LIMITS).forEach(key => {
          const expected = EXPECTED_FREE_LIMITS[key]
          const actual = calculatedLimits[key]
          const isCorrect = expected === actual
          
          console.log(`   ${isCorrect ? '✅' : '❌'} ${key}: expected ${expected}, got ${actual}`)
          if (!isCorrect) allCorrect = false
        })

        if (allCorrect) {
          console.log('🎉 All free plan limits are correct!')
        } else {
          console.log('❌ Some free plan limits are incorrect!')
        }

        // Check for unwanted subscriptions
        if (subscriptionRecords.length > 0) {
          console.log('\n⚠️  WARNING: Free user has subscription records!')
          console.log('   This should not happen. Free users should have no subscriptions.')
          return false
        } else {
          console.log('\n✅ Correct: Free user has no subscription records')
        }
      }
    } else {
      console.log('❌ No usage record found - this is a problem!')
      return false
    }

    return true

  } catch (error) {
    console.error('❌ Error verifying user:', error)
    return false
  }
}

async function verifySystemHealth() {
  console.log('🏥 System Health Check')
  console.log('─'.repeat(50))

  try {
    // Test plan limits function
    console.log('🧪 Testing getPlanLimits function:')
    
    const testCases = [
      { plan: 'free', expected: EXPECTED_FREE_LIMITS },
      { plan: 'basic-monthly', expected: { projects: 10, videoExports: 15, aiImages: 100 } },
      { plan: 'premium-monthly', expected: { projects: 20, videoExports: 30, aiImages: 200 } }
    ]

    let allTestsPassed = true

    for (const testCase of testCases) {
      const limits = getPlanLimits(testCase.plan)
      console.log(`\n   Testing ${testCase.plan}:`)
      
      Object.keys(testCase.expected).forEach(key => {
        const expected = testCase.expected[key]
        const actual = limits[key]
        const passed = expected === actual
        
        console.log(`   ${passed ? '✅' : '❌'} ${key}: expected ${expected}, got ${actual}`)
        if (!passed) allTestsPassed = false
      })
    }

    if (allTestsPassed) {
      console.log('\n🎉 All plan limit tests passed!')
    } else {
      console.log('\n❌ Some plan limit tests failed!')
    }

    // Check database consistency
    console.log('\n📊 Database Consistency Check:')
    
    const totalUsers = await db.select().from(user)
    const totalUsage = await db.select().from(usage)
    const totalSubscriptions = await db.select().from(subscription)
    
    console.log(`   Total users: ${totalUsers.length}`)
    console.log(`   Total usage records: ${totalUsage.length}`)
    console.log(`   Total subscriptions: ${totalSubscriptions.length}`)

    // Count free vs paid users
    const freeUsage = totalUsage.filter(u => u.planType === 'free')
    const paidUsage = totalUsage.filter(u => u.planType !== 'free')
    
    console.log(`   Free plan users: ${freeUsage.length}`)
    console.log(`   Paid plan users: ${paidUsage.length}`)

    return allTestsPassed

  } catch (error) {
    console.error('❌ System health check failed:', error)
    return false
  }
}

async function main() {
  const userEmail = process.argv[2]

  if (userEmail) {
    const success = await verifyUser(userEmail)
    process.exit(success ? 0 : 1)
  } else {
    console.log('🔍 Running comprehensive system verification...')
    const success = await verifySystemHealth()
    
    console.log('\n📋 Verification Complete!')
    if (success) {
      console.log('✅ System is working correctly')
    } else {
      console.log('❌ System has issues that need to be addressed')
    }
    
    process.exit(success ? 0 : 1)
  }
}

// Handle script termination
process.on('SIGINT', async () => {
  console.log('\n⚠️  Script interrupted by user')
  await client.end()
  process.exit(0)
})

// Run the script
main().catch(async (error) => {
  console.error('❌ Unhandled error:', error)
  await client.end()
  process.exit(1)
}).finally(() => {
  client.end()
})
