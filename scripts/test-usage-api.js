#!/usr/bin/env node

/**
 * Test Usage API Script
 * 
 * This script tests the usage API to ensure it returns correct responses
 * for different user scenarios.
 * 
 * Usage: bun run scripts/test-usage-api.js
 */

import { drizzle } from 'drizzle-orm/postgres-js'
import { eq } from 'drizzle-orm'
import postgres from 'postgres'
import { user, usage, subscription } from '../src/db/schema.js'

// Database connection
const connectionString = process.env.DATABASE_URL
if (!connectionString) {
  console.error('❌ DATABASE_URL environment variable is required')
  process.exit(1)
}

const client = postgres(connectionString)
const db = drizzle(client)

// Simulate the usage API logic
async function simulateUsageAPI(userId, organizationId = null) {
  const referenceId = organizationId || userId

  // Get usage record
  let usageRecord = await db
    .select()
    .from(usage)
    .where(eq(usage.organizationId, referenceId))
    .limit(1)

  if (!usageRecord || usageRecord.length === 0) {
    // Create new usage record with default values
    const [newUsageRecord] = await db
      .insert(usage)
      .values({
        organizationId: referenceId,
        members: [userId],
        planType: 'free',
        planStatus: 'active',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      })
      .returning()

    usageRecord = [newUsageRecord]
  }

  const currentUsage = usageRecord[0]

  // Get active subscription
  const activeSubscription = await db
    .select()
    .from(subscription)
    .where(eq(subscription.referenceId, referenceId))
    .limit(1)

  // Determine plan type and limits
  let planType = currentUsage.planType
  let planStatus = currentUsage.planStatus
  let periodStart = currentUsage.currentPeriodStart
  let periodEnd = currentUsage.currentPeriodEnd

  // Only override with subscription data if it's for a PAID plan
  if (activeSubscription.length > 0) {
    const sub = activeSubscription[0]
    
    if (sub.plan && sub.plan.toLowerCase() !== 'free') {
      planType = sub.plan
      planStatus = sub.status || 'active'
      periodStart = sub.periodStart || currentUsage.currentPeriodStart
      periodEnd = sub.periodEnd || currentUsage.currentPeriodEnd
    }
  }

  // Get plan limits (simplified version)
  const planLimits = getPlanLimitsSimulated(planType)

  return {
    plan: {
      type: planType,
      status: planStatus,
      periodStart: periodStart,
      periodEnd: periodEnd,
    },
    usage: {
      projects: {
        used: currentUsage.projectsUsed || 0,
        max: planLimits.projects,
        percentage: planLimits.projects > 0 
          ? Math.round(((currentUsage.projectsUsed || 0) / planLimits.projects) * 100)
          : 0,
      },
      videoExports: {
        used: currentUsage.videoExportsUsed || 0,
        max: planLimits.videoExports,
        percentage: planLimits.videoExports > 0 
          ? Math.round(((currentUsage.videoExportsUsed || 0) / planLimits.videoExports) * 100)
          : 0,
      },
      aiImages: {
        used: currentUsage.aiImagesUsed || 0,
        max: planLimits.aiImages,
        percentage: planLimits.aiImages > 0 
          ? Math.round(((currentUsage.aiImagesUsed || 0) / planLimits.aiImages) * 100)
          : 0,
      },
      teamMembers: {
        used: currentUsage.teamMembersUsed || 0,
        max: planLimits.teamMembers,
        percentage: planLimits.teamMembers > 0 
          ? Math.round(((currentUsage.teamMembersUsed || 0) / planLimits.teamMembers) * 100)
          : 0,
      },
      storage: {
        used: currentUsage.storageUsed || 0,
        max: planLimits.storage,
        percentage: planLimits.storage > 0 
          ? Math.round(((currentUsage.storageUsed || 0) / planLimits.storage) * 100)
          : 0,
      },
    },
  }
}

// Simplified plan limits function
function getPlanLimitsSimulated(planName) {
  const plans = {
    'free': {
      projects: 3,
      videoExports: 0,
      aiImages: 20,
      storage: 100,
      teamMembers: 1,
    },
    'basic-monthly': {
      projects: 10,
      videoExports: 15,
      aiImages: 100,
      storage: 500,
      teamMembers: 1,
    },
    'premium-monthly': {
      projects: 20,
      videoExports: 30,
      aiImages: 200,
      storage: 1024,
      teamMembers: 5,
    }
  }

  return plans[planName.toLowerCase()] || plans['free']
}

async function testScenarios() {
  console.log('🧪 Testing Usage API Scenarios')
  console.log('─'.repeat(50))

  try {
    // Get a test user
    const testUsers = await db.select().from(user).limit(3)
    
    if (testUsers.length === 0) {
      console.log('❌ No users found in database for testing')
      return false
    }

    let allTestsPassed = true

    for (let i = 0; i < Math.min(testUsers.length, 2); i++) {
      const testUser = testUsers[i]
      console.log(`\n👤 Testing User: ${testUser.email}`)

      // Test the usage API simulation
      const result = await simulateUsageAPI(testUser.id)
      
      console.log('📊 API Response:')
      console.log(`   Plan: ${result.plan.type} (${result.plan.status})`)
      console.log(`   Projects: ${result.usage.projects.used}/${result.usage.projects.max}`)
      console.log(`   Video Exports: ${result.usage.videoExports.used}/${result.usage.videoExports.max}`)
      console.log(`   AI Images: ${result.usage.aiImages.used}/${result.usage.aiImages.max}`)
      console.log(`   Team Members: ${result.usage.teamMembers.used}/${result.usage.teamMembers.max}`)
      console.log(`   Storage: ${result.usage.storage.used}/${result.usage.storage.max}MB`)

      // Verify free plan users get correct limits
      if (result.plan.type === 'free') {
        const expectedLimits = {
          projects: 3,
          videoExports: 0,
          aiImages: 20,
          teamMembers: 1,
          storage: 100
        }

        console.log('\n✅ Verifying Free Plan Limits:')
        let userTestPassed = true

        Object.keys(expectedLimits).forEach(key => {
          const expected = expectedLimits[key]
          const actual = result.usage[key].max
          const isCorrect = expected === actual

          console.log(`   ${isCorrect ? '✅' : '❌'} ${key}: expected ${expected}, got ${actual}`)
          if (!isCorrect) {
            userTestPassed = false
            allTestsPassed = false
          }
        })

        if (userTestPassed) {
          console.log('   🎉 Free plan limits are correct!')
        } else {
          console.log('   ❌ Free plan limits are incorrect!')
        }
      }
    }

    return allTestsPassed

  } catch (error) {
    console.error('❌ Test failed:', error)
    return false
  }
}

async function main() {
  const success = await testScenarios()
  
  console.log('\n📋 Test Results:')
  if (success) {
    console.log('✅ All tests passed! Usage API is working correctly.')
  } else {
    console.log('❌ Some tests failed. Please check the issues above.')
  }
  
  await client.end()
  process.exit(success ? 0 : 1)
}

// Handle script termination
process.on('SIGINT', async () => {
  console.log('\n⚠️  Script interrupted by user')
  await client.end()
  process.exit(0)
})

// Run the script
main().catch(async (error) => {
  console.error('❌ Unhandled error:', error)
  await client.end()
  process.exit(1)
})
