#!/usr/bin/env node

/**
 * Fix Free Plan Usage Data Script
 * 
 * This script corrects usage records for users who should be on the free plan
 * but may have incorrect plan data due to the billing period bug.
 * 
 * Usage: bun run scripts/fix-free-plan-usage.js [--dry-run]
 */

import { drizzle } from 'drizzle-orm/postgres-js'
import { eq, and, isNull } from 'drizzle-orm'
import postgres from 'postgres'
import { usage, subscription } from '../src/db/schema.js'

// Database connection
const connectionString = process.env.DATABASE_URL
if (!connectionString) {
  console.error('❌ DATABASE_URL environment variable is required')
  process.exit(1)
}

const client = postgres(connectionString)
const db = drizzle(client)

// Parse command line arguments
const isDryRun = process.argv.includes('--dry-run')

async function main() {
  console.log('🔍 Starting Free Plan Usage Data Fix...')
  console.log(`Mode: ${isDryRun ? 'DRY RUN' : 'LIVE UPDATE'}`)
  console.log('─'.repeat(50))

  try {
    // Step 1: Find all usage records that should be free plan
    console.log('📊 Finding usage records that should be on free plan...')
    
    const usageRecords = await db
      .select()
      .from(usage)
      .where(eq(usage.planType, 'free'))

    console.log(`Found ${usageRecords.length} usage records with planType 'free'`)

    // Step 2: Check which of these users have active subscriptions
    const recordsToFix = []
    
    for (const record of usageRecords) {
      // Check if this organization has any active subscriptions
      const activeSubscriptions = await db
        .select()
        .from(subscription)
        .where(
          and(
            eq(subscription.referenceId, record.organizationId),
            eq(subscription.status, 'active')
          )
        )

      // If no active subscription, this should definitely be free plan
      if (activeSubscriptions.length === 0) {
        recordsToFix.push({
          record,
          reason: 'No active subscription - should be free plan',
          hasSubscription: false
        })
      } else {
        console.log(`⚠️  Organization ${record.organizationId} has active subscription but usage record shows 'free' plan`)
      }
    }

    console.log(`\n📋 Summary:`)
    console.log(`- Total usage records checked: ${usageRecords.length}`)
    console.log(`- Records that need fixing: ${recordsToFix.length}`)

    if (recordsToFix.length === 0) {
      console.log('✅ No records need fixing!')
      return
    }

    // Step 3: Display what will be fixed
    console.log('\n🔧 Records to be fixed:')
    recordsToFix.forEach((item, index) => {
      console.log(`${index + 1}. Organization: ${item.record.organizationId}`)
      console.log(`   Current planType: ${item.record.planType}`)
      console.log(`   Reason: ${item.reason}`)
      console.log(`   Members: ${item.record.members.length}`)
      console.log('')
    })

    if (isDryRun) {
      console.log('🔍 DRY RUN MODE - No changes will be made')
      console.log('Run without --dry-run to apply fixes')
      return
    }

    // Step 4: Apply fixes
    console.log('🚀 Applying fixes...')
    
    let fixedCount = 0
    for (const item of recordsToFix) {
      try {
        await db
          .update(usage)
          .set({
            planType: 'free',
            planStatus: 'active',
            updatedAt: new Date(),
          })
          .where(eq(usage.id, item.record.id))

        fixedCount++
        console.log(`✅ Fixed organization ${item.record.organizationId}`)
      } catch (error) {
        console.error(`❌ Failed to fix organization ${item.record.organizationId}:`, error.message)
      }
    }

    console.log('\n🎉 Fix completed!')
    console.log(`Successfully fixed: ${fixedCount}/${recordsToFix.length} records`)

  } catch (error) {
    console.error('❌ Script failed:', error)
    process.exit(1)
  } finally {
    await client.end()
  }
}

// Handle script termination
process.on('SIGINT', async () => {
  console.log('\n⚠️  Script interrupted by user')
  await client.end()
  process.exit(0)
})

process.on('SIGTERM', async () => {
  console.log('\n⚠️  Script terminated')
  await client.end()
  process.exit(0)
})

// Run the script
main().catch(async (error) => {
  console.error('❌ Unhandled error:', error)
  await client.end()
  process.exit(1)
})
