#!/usr/bin/env node

/**
 * Fix User Plan System Script
 * 
 * This script fixes the user plan and usage system by:
 * 1. Removing unwanted subscription records for free users
 * 2. Ensuring free users have correct usage records
 * 3. Verifying paid users have correct subscription records
 * 
 * Usage: bun run scripts/fix-user-plan-system.js [--dry-run]
 */

import { drizzle } from 'drizzle-orm/postgres-js'
import { eq, and, or, isNull, sql } from 'drizzle-orm'
import postgres from 'postgres'
import { user, usage, subscription, organization, member } from '../src/db/schema.js'

// Database connection
const connectionString = process.env.DATABASE_URL
if (!connectionString) {
  console.error('❌ DATABASE_URL environment variable is required')
  process.exit(1)
}

const client = postgres(connectionString)
const db = drizzle(client)

// Parse command line arguments
const isDryRun = process.argv.includes('--dry-run')

async function main() {
  console.log('🔧 Starting User Plan System Fix...')
  console.log(`Mode: ${isDryRun ? 'DRY RUN' : 'LIVE FIX'}`)
  console.log('─'.repeat(60))

  try {
    // Step 1: Find all users and their current state
    console.log('📊 Analyzing current user plan state...')
    
    const allUsers = await db.select().from(user)
    console.log(`Found ${allUsers.length} total users`)

    let freeUsersWithSubscriptions = []
    let usersWithoutUsageRecords = []
    let validPaidUsers = []

    for (const userData of allUsers) {
      // Get user's organizations
      const userOrgs = await db
        .select({ orgId: organization.id })
        .from(member)
        .innerJoin(organization, eq(member.organizationId, organization.id))
        .where(eq(member.userId, userData.id))

      const referenceIds = [userData.id, ...userOrgs.map(org => org.orgId)]

      // Check usage records
      const usageRecords = []
      for (const refId of referenceIds) {
        const records = await db
          .select()
          .from(usage)
          .where(eq(usage.organizationId, refId))
        usageRecords.push(...records)
      }

      // Check subscription records
      const subscriptionRecords = []
      for (const refId of referenceIds) {
        const records = await db
          .select()
          .from(subscription)
          .where(eq(subscription.referenceId, refId))
        subscriptionRecords.push(...records)
      }

      // Categorize users
      if (usageRecords.length === 0) {
        usersWithoutUsageRecords.push({
          user: userData,
          referenceIds,
          subscriptions: subscriptionRecords
        })
      } else {
        const freeUsageRecords = usageRecords.filter(r => r.planType === 'free')
        const paidSubscriptions = subscriptionRecords.filter(s => 
          s.plan && s.plan.toLowerCase() !== 'free' && 
          (s.status === 'active' || s.status === 'trialing')
        )

        if (freeUsageRecords.length > 0 && subscriptionRecords.length > 0) {
          // Free users with unwanted subscriptions
          freeUsersWithSubscriptions.push({
            user: userData,
            referenceIds,
            usageRecords: freeUsageRecords,
            subscriptions: subscriptionRecords
          })
        } else if (paidSubscriptions.length > 0) {
          // Valid paid users
          validPaidUsers.push({
            user: userData,
            referenceIds,
            usageRecords,
            subscriptions: paidSubscriptions
          })
        }
      }
    }

    // Step 2: Report findings
    console.log('\n📋 Analysis Results:')
    console.log(`- Users without usage records: ${usersWithoutUsageRecords.length}`)
    console.log(`- Free users with unwanted subscriptions: ${freeUsersWithSubscriptions.length}`)
    console.log(`- Valid paid users: ${validPaidUsers.length}`)

    // Step 3: Fix free users with unwanted subscriptions
    if (freeUsersWithSubscriptions.length > 0) {
      console.log('\n🧹 Fixing free users with unwanted subscriptions:')
      
      for (const item of freeUsersWithSubscriptions) {
        console.log(`\n👤 User: ${item.user.email}`)
        console.log(`   Unwanted subscriptions: ${item.subscriptions.length}`)
        
        item.subscriptions.forEach(sub => {
          console.log(`   - ${sub.plan} (${sub.status}) - ID: ${sub.id}`)
        })

        if (!isDryRun) {
          // Delete unwanted subscription records
          for (const sub of item.subscriptions) {
            try {
              await db
                .delete(subscription)
                .where(eq(subscription.id, sub.id))
              console.log(`   ✅ Deleted subscription ${sub.id}`)
            } catch (error) {
              console.error(`   ❌ Failed to delete subscription ${sub.id}:`, error.message)
            }
          }
        }
      }
    }

    // Step 4: Create missing usage records
    if (usersWithoutUsageRecords.length > 0) {
      console.log('\n📝 Creating missing usage records:')
      
      for (const item of usersWithoutUsageRecords) {
        console.log(`\n👤 User: ${item.user.email}`)
        
        // Use primary reference ID (user ID or first org ID)
        const primaryRefId = item.referenceIds[0]
        
        if (!isDryRun) {
          try {
            await db
              .insert(usage)
              .values({
                organizationId: primaryRefId,
                members: [item.user.id],
                planType: 'free',
                planStatus: 'active',
                currentPeriodStart: new Date(),
                currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                projectsUsed: 0,
                videoExportsUsed: 0,
                aiImagesUsed: 0,
                teamMembersUsed: 1,
                storageUsed: 0,
              })
            console.log(`   ✅ Created usage record for ${primaryRefId}`)
          } catch (error) {
            console.error(`   ❌ Failed to create usage record:`, error.message)
          }
        }
      }
    }

    // Step 5: Summary
    console.log('\n🎉 Fix Summary:')
    if (isDryRun) {
      console.log('🔍 DRY RUN MODE - No changes were made')
      console.log('Run without --dry-run to apply fixes')
    } else {
      console.log(`✅ Fixed ${freeUsersWithSubscriptions.length} free users with unwanted subscriptions`)
      console.log(`✅ Created ${usersWithoutUsageRecords.length} missing usage records`)
      console.log(`✅ Preserved ${validPaidUsers.length} valid paid user subscriptions`)
    }

    console.log('\n📊 Final State:')
    console.log('- Free users: No subscription records, usage records with planType="free"')
    console.log('- Paid users: Valid subscription records, usage records match subscription plan')

  } catch (error) {
    console.error('❌ Script failed:', error)
    process.exit(1)
  } finally {
    await client.end()
  }
}

// Handle script termination
process.on('SIGINT', async () => {
  console.log('\n⚠️  Script interrupted by user')
  await client.end()
  process.exit(0)
})

// Run the script
main().catch(async (error) => {
  console.error('❌ Unhandled error:', error)
  await client.end()
  process.exit(1)
})
