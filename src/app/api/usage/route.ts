import { NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { usage, subscription } from '@/db/schema'
import { eq, sql } from 'drizzle-orm'
import { getUserSession } from '@/lib/user-utils'
import { getPlanLimits as getPlanLimitsFromUtils } from '@/lib/plan-utils'

import { getBillingPeriod } from '@/lib/subscription-utils'

export async function GET() {
  try {
    const session = await getUserSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id
    const activeOrganizationId = session?.session?.activeOrganizationId

    // Use active organization ID if available, otherwise use user ID
    const referenceId = activeOrganizationId || userId

    // Get usage record for the organization or user
    let usageRecord = await db
      .select()
      .from(usage)
      .where(eq(usage.organizationId, referenceId))
      .limit(1)
    console.log('Pre-usageRecord', usageRecord)

    if (!usageRecord || usageRecord.length === 0) {
      // Create new usage record with default values
      const [newUsageRecord] = await db
        .insert(usage)
        .values({
          organizationId: referenceId,
          members: [userId], // Start with the current user
          planType: 'free', // Use lowercase to match constants
          planStatus: 'active',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        })
        .returning()

      usageRecord = [newUsageRecord]
      console.log('Post-usageRecord', usageRecord)
    } else {
      // Check if the current user is in the members array, if not add them
      const currentRecord = usageRecord[0]
      if (!currentRecord.members.includes(userId)) {
        await db
          .update(usage)
          .set({
            members: sql`array_append(${usage.members}, ${userId})`,
            updatedAt: new Date(),
          })
          .where(eq(usage.organizationId, referenceId))
      }
      console.log('Post-currentRecord', currentRecord)
    }

    const currentUsage = usageRecord[0]
    console.log('currentUsage', currentUsage)

    // Get active subscription for this referenceId to override plan info
    const activeSubscription = await db
      .select()
      .from(subscription)
      .where(
        eq(subscription.referenceId, referenceId) &&
          sql`${subscription.status} IN ('active', 'trialing')`
      )
      .orderBy(sql`${subscription.periodStart} DESC`)
      .limit(1)
    console.log('activeSubscription', activeSubscription)

    // Determine plan type and limits
    let planType = currentUsage.planType // fallback to stored plan
    let planStatus = currentUsage.planStatus
    let periodStart = currentUsage.currentPeriodStart
    let periodEnd = currentUsage.currentPeriodEnd

    if (activeSubscription.length > 0) {
      // Use subscription as source of truth
      const sub = activeSubscription[0]
      planType = sub.plan
      planStatus = sub.status || 'active'
      periodStart = sub.periodStart || currentUsage.currentPeriodStart
      periodEnd = sub.periodEnd || currentUsage.currentPeriodEnd
    }

    // Get billing period for plan limits calculation
    // Free plans don't have billing periods (they're forever), so skip billing period logic
    let planLimits
    if (planType.toLowerCase() === 'free') {
      planLimits = getPlanLimitsFromUtils(planType) // No billing period for free plan
      console.log('planLimits1', planLimits)
    } else {
      const billingPeriod = getBillingPeriod(
        periodStart || undefined,
        periodEnd || undefined
      )
      planLimits = getPlanLimitsFromUtils(planType, billingPeriod)
      console.log('planLimits2', planLimits)
    }

    // Calculate usage percentages and format response
    const usageData = {
      plan: {
        type: planType,
        status: planStatus,
        periodStart: periodStart,
        periodEnd: periodEnd,
      },
      usage: {
        projects: {
          used: currentUsage.projectsUsed || 0,
          max: planLimits.projects,
          percentage:
            planLimits.projects > 0
              ? Math.round(
                  ((currentUsage.projectsUsed || 0) / planLimits.projects) * 100
                )
              : 0,
        },
        videoExports: {
          used: currentUsage.videoExportsUsed || 0,
          max: planLimits.videoExports,
          percentage:
            planLimits.videoExports > 0
              ? Math.round(
                  ((currentUsage.videoExportsUsed || 0) /
                    planLimits.videoExports) *
                    100
                )
              : 0,
        },
        aiImages: {
          used: currentUsage.aiImagesUsed || 0,
          max: planLimits.aiImages,
          percentage:
            planLimits.aiImages > 0
              ? Math.round(
                  ((currentUsage.aiImagesUsed || 0) / planLimits.aiImages) * 100
                )
              : 0,
        },
        teamMembers: {
          used: currentUsage.teamMembersUsed || 0,
          max: planLimits.teamMembers,
          percentage:
            planLimits.teamMembers > 0
              ? Math.round(
                  ((currentUsage.teamMembersUsed || 0) /
                    planLimits.teamMembers) *
                    100
                )
              : 0,
        },
        storage: {
          used: currentUsage.storageUsed || 0,
          max: planLimits.storage,
          percentage:
            planLimits.storage > 0
              ? Math.round(
                  ((currentUsage.storageUsed || 0) / planLimits.storage) * 100
                )
              : 0,
        },
      },
    }

    return NextResponse.json(usageData)
  } catch (error) {
    console.error('Usage API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
